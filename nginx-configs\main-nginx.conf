events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 로그 설정
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 기본 설정
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # gzip 압축
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 메인 nginx 서버 (domain.co.kr 역할)
    server {
        listen 80;
        server_name localhost;

        # 기본 루트 페이지 (테스트용)
        location / {
            default_type text/html;
            return 200 '<html><body><h1>Main Nginx Server (domain.co.kr)</h1><p><a href="/nlpm-svc2d/ai">Go to Chatbot (/nlpm-svc2d/ai)</a></p><p>Available paths:</p><ul><li><a href="/nlpm-svc2d/">/nlpm-svc2d/</a> - Sub nginx server</li><li><a href="/nlpm-svc2d/ai">/nlpm-svc2d/ai</a> - Chatbot service</li></ul></body></html>';
        }

        # /nlpm-svc2d 경로로 들어오는 모든 요청을 서브 nginx로 프록시
        # 경로를 제거하고 서브 nginx의 루트로 전달
        location /nlpm-svc2d/ {
            proxy_pass              http://sub-nginx/;
            proxy_http_version      1.1;
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
        }

        # 에러 페이지
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}
