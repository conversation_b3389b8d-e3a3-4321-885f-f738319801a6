
/** @type {import('next').NextConfig} */
const nextConfig = {
    output: "standalone", // for self-hosting

    basePath: process.env.NEXT_BASE_PATH || '',
    assetPrefix: process.env.NEXT_ASSET_PREFIX || '',
    env: {
        NEXT_PUBLIC_BASE_PATH: process.env.NEXT_BASE_PATH || '',
    },

    trailingSlash: true,

    eslint: {
        // 빌드 시 ESLint 오류를 무시 (개발 중에는 IDE에서 확인)
        ignoreDuringBuilds: true,
    },
    pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],

    // 헤더 설정 (프록시 환경에서 필요할 수 있음)
    // async headers() {
    //     return [
    //         {
    //             source: '/(.*)',
    //             headers: [
    //                 {
    //                     key: 'X-Frame-Options',
    //                     value: 'SAMEORIGIN',
    //                 },
    //                 {
    //                     key: 'X-Content-Type-Options',
    //                     value: 'nosniff',
    //                 },
    //             ],
    //         },
    //     ];
    // },

};

export default nextConfig

