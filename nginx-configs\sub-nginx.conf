events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 로그 설정
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 기본 설정
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # gzip 압축
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 서브 nginx 서버 (nlpm-svc2d 서버 역할)
    server {
        listen 80;
        server_name localhost;

        # /ai/ 경로로 들어오는 모든 요청을 Next.js로 프록시
        location /ai {
            proxy_pass              http://lx-chatbot:3000;
            proxy_http_version      1.1;
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;

            # 스트리밍 관련 설정
            proxy_buffering         off;
            proxy_cache             off;
            proxy_request_buffering off;
            proxy_read_timeout      3600s;
            chunked_transfer_encoding on;
        }

        # 기본 루트 페이지 (테스트용)
        location / {
            return 200 '<html><body><h1>Sub Nginx Server (nlpm-svc2d)</h1><p><a href="/ai">Go to Chatbot (/ai)</a></p><p>This server handles requests from main nginx at /nlpm-svc2d/</p></body></html>';
            add_header Content-Type text/html;
        }

        # 에러 페이지
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}
